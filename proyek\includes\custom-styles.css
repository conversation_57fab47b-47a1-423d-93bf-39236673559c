/* Custom Styles for Antosa Arsitek Project Management System */

/* Sidebar Enhancements */
.sidebar-brand-text {
    font-size: 0.9rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.sidebar-brand-icon {
    font-size: 1.5rem;
}

/* Collapsible Menu Items */
.collapse-item {
    padding: 0.5rem 1rem;
    margin: 0.1rem 0;
    border-radius: 0.35rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #5a5c69;
}

.collapse-item i {
    margin-right: 0.5rem;
    width: 16px;
    text-align: center;
}

.collapse-item:hover {
    background-color: #f8f9fc;
    transform: translateX(5px);
    text-decoration: none;
    color: #3a3b45;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.collapse-item.active {
    background-color: #4e73df !important;
    color: white !important;
    font-weight: 600;
}

.collapse-item.active i {
    color: white !important;
}

.collapse-item.active:hover {
    background-color: #375a7f !important;
    color: white !important;
    text-decoration: none;
}

.collapse-item.active:hover i {
    color: white !important;
}

/* Badge Counter */
.badge-counter {
    position: absolute;
    top: 6px;
    right: 6px;
    font-size: 0.7rem;
    min-width: 1.2rem;
    height: 1.2rem;
    border-radius: 50%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    line-height: 1;
    border: 2px solid #fff;
    box-shadow: 0 3px 10px rgba(0,0,0,0.3);
    z-index: 1001;
    background-color: #dc3545 !important;
    color: #fff !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Breadcrumb Styling */
.breadcrumb {
    font-size: 0.85rem;
    background-color: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    font-weight: bold;
    color: #6c757d;
}

.breadcrumb-item a {
    color: #6c757d;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #4e73df;
    text-decoration: none;
}

.breadcrumb-item.active {
    color: #495057;
    font-weight: 600;
}

/* Notification Icons */
.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Dropdown Enhancements */
.dropdown-list {
    max-width: 20rem;
    min-width: 18rem;
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

/* Topbar Navigation Alignment */
.topbar .navbar-nav {
    align-items: center;
    height: 100%;
}

.topbar .nav-item {
    display: flex;
    align-items: center;
    height: 100%;
}

.topbar .nav-link {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1rem;
    height: 3rem;
    transition: all 0.2s ease;
    position: relative;
}

.topbar .nav-link:hover {
    background-color: rgba(0,0,0,0.05);
    border-radius: 0.35rem;
}

.topbar .nav-link i {
    font-size: 1.1rem;
    color: #5a5c69;
}

.topbar .nav-link:hover i {
    color: #4e73df;
}

/* Ensure vertical centering for all topbar elements */
.topbar {
    min-height: 4.375rem;
}

.topbar .navbar-nav .nav-item .nav-link {
    line-height: 1;
}

/* Special styling for notification bell */
.topbar .nav-item.dropdown .nav-link {
    min-width: 3rem;
    position: relative;
    overflow: visible;
}

/* Notification bell icon positioning */
.topbar .nav-item.dropdown .nav-link i.fa-bell {
    position: relative;
    display: inline-block;
}

/* Badge positioning relative to bell icon */
.topbar .nav-item.dropdown .nav-link .badge-counter {
    position: absolute;
    top: 6px;
    right: 6px;
    transform: none;
    visibility: visible !important;
    opacity: 1 !important;
    display: flex !important;
}

/* Ensure badge is always visible and positioned correctly */
.topbar .nav-item.dropdown:hover .badge-counter,
.topbar .nav-item.dropdown .nav-link:hover .badge-counter {
    visibility: visible !important;
    opacity: 1 !important;
    display: flex !important;
}

/* User dropdown text alignment */
.topbar .nav-item .nav-link span {
    vertical-align: middle;
}

/* Profile image styling */
.img-profile {
    width: 2rem;
    height: 2rem;
    object-fit: cover;
}

/* Topbar divider alignment */
.topbar-divider {
    height: 2rem;
    margin: auto 1rem;
    align-self: center;
}

/* Notification Dropdown Styling */
.dropdown-item.d-flex {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e3e6f0;
}

.dropdown-item.d-flex:last-child {
    border-bottom: none;
}

.dropdown-item.d-flex:hover {
    background-color: #f8f9fc;
    transform: translateX(2px);
    transition: all 0.2s ease;
}



/* Notification Dropdown Header */
.dropdown-header.bg-primary {
    margin: 0;
    border-radius: 0.35rem 0.35rem 0 0;
}

/* Icon Circle Flex Shrink */
.icon-circle {
    flex-shrink: 0;
}

.dropdown-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
}

/* Active Navigation Items */
.nav-item.active .nav-link {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-radius: 0.35rem;
    font-weight: 600;
    color: #fff !important;
}

.nav-item.active .nav-link span {
    color: #fff !important;
}

.nav-item.active .nav-link i {
    color: #fff !important;
}

/* Sidebar Navigation Links - Consolidated */
.sidebar .nav-link,
.sidebar .nav-link span,
.sidebar .nav-link i {
    color: rgba(255, 255, 255, 0.8) !important;
}

.sidebar .nav-link:hover,
.sidebar .nav-link:hover span,
.sidebar .nav-link:hover i {
    color: #fff !important;
}

/* Collapsed Navigation States */
.sidebar .nav-link.collapsed:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Expanded Navigation States */
.sidebar .nav-link:not(.collapsed) {
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
}

.sidebar .nav-link:not(.collapsed) span,
.sidebar .nav-link:not(.collapsed) i {
    color: #fff !important;
}

/* Folder Icons Animation */
.nav-link[data-toggle="collapse"] i {
    transition: transform 0.3s ease;
}

.nav-link[data-toggle="collapse"]:not(.collapsed) i {
    transform: rotate(90deg);
}

/* Card Enhancements */
.card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
    transform: translateY(-2px);
}

/* Table Enhancements */
.table {
    border-radius: 0.35rem;
    overflow: hidden;
}

.table thead th {
    border-top: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

/* Button Enhancements */
.btn {
    border-radius: 0.35rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 1rem 0 rgba(58, 59, 69, 0.2);
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Footer Enhancements */
.sticky-footer {
    border-top: 1px solid #e3e6f0;
}

.copyright {
    font-size: 0.9rem;
}

/* Modal Enhancements */
.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 1px solid #e3e6f0;
    background-color: #f8f9fc;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
    background-color: #f8f9fc;
}

/* Enhanced Responsive Design */

/* Extra Small Devices (phones, 576px and down) */
@media (max-width: 576px) {
    /* Typography */
    .breadcrumb {
        font-size: 0.7rem;
        padding: 0.25rem 0;
    }

    .breadcrumb-item {
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Sidebar Optimizations */
    .collapse-item {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }

    .sidebar-brand-text {
        font-size: 0.75rem;
    }

    .sidebar-brand-icon {
        font-size: 1.2rem;
    }

    /* Navigation */
    .sidebar .nav-item .nav-link {
        padding: 0.6rem 1rem;
        min-height: 2.8rem;
    }

    .sidebar .nav-item .nav-link i {
        margin-right: 0.6rem;
        min-width: 1.2rem;
    }

    /* Topbar */
    .topbar .nav-link {
        height: 2.3rem;
        padding: 0.4rem 0.6rem;
    }

    .topbar .h3 {
        font-size: 1rem;
    }

    /* Notifications */
    .dropdown-list {
        min-width: 14rem;
        max-width: 90vw;
    }

    .badge-counter {
        min-width: 0.9rem;
        height: 0.9rem;
        font-size: 0.55rem;
    }

    .topbar .nav-item.dropdown .nav-link .badge-counter {
        top: 0.15rem;
        right: 0.15rem;
    }

    .img-profile {
        width: 1.6rem;
        height: 1.6rem;
    }

    /* Cards and Content */
    .card {
        margin-bottom: 1rem;
    }

    .card-header {
        padding: 0.75rem 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    /* Tables */
    .table-responsive {
        font-size: 0.8rem;
    }

    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
    }

    /* Buttons */
    .btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    /* Forms */
    .form-control {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    /* Modals */
    .modal-dialog {
        margin: 1rem 0.5rem;
        max-width: calc(100% - 1rem);
    }

    .modal-header {
        padding: 1rem 1rem 0.5rem;
    }

    .modal-body {
        padding: 0.5rem 1rem;
    }

    .modal-footer {
        padding: 0.5rem 1rem 1rem;
        flex-direction: column;
    }

    .modal-footer .btn {
        width: 100%;
        margin: 0.25rem 0;
    }
}

/* Small Devices (tablets, 768px and down) */
@media (max-width: 768px) {
    /* Typography */
    .breadcrumb {
        font-size: 0.75rem;
    }

    .breadcrumb-item {
        max-width: 100px;
    }

    /* Sidebar */
    .collapse-item {
        padding: 0.45rem 0.9rem;
        font-size: 0.9rem;
    }

    .sidebar-brand-text {
        font-size: 0.8rem;
    }

    .sidebar-brand-icon {
        font-size: 1.3rem;
    }

    /* Navigation */
    .sidebar .nav-item .nav-link {
        padding: 0.65rem 1rem;
        min-height: 2.9rem;
    }

    /* Topbar */
    .topbar .nav-link {
        height: 2.5rem;
        padding: 0.5rem 0.75rem;
    }

    .topbar .h3 {
        font-size: 1.1rem;
    }

    /* Notifications */
    .dropdown-list {
        min-width: 16rem;
        max-width: 90vw;
    }

    .badge-counter {
        min-width: 1rem;
        height: 1rem;
        font-size: 0.6rem;
    }

    .topbar .nav-item.dropdown .nav-link .badge-counter {
        top: 0.2rem;
        right: 0.2rem;
    }

    .img-profile {
        width: 1.8rem;
        height: 1.8rem;
    }

    /* Cards */
    .card-header {
        padding: 0.875rem 1.25rem;
    }

    .card-body {
        padding: 1.25rem;
    }

    /* Tables */
    .table-responsive {
        font-size: 0.85rem;
    }

    .table th,
    .table td {
        padding: 0.6rem 0.4rem;
    }

    /* Buttons */
    .btn {
        padding: 0.55rem 0.85rem;
        font-size: 0.9rem;
    }

    /* Forms */
    .form-control {
        padding: 0.55rem 0.85rem;
        font-size: 0.95rem;
    }

    /* Modals */
    .modal-dialog {
        margin: 1.5rem 1rem;
        max-width: calc(100% - 2rem);
    }

    .modal-footer {
        flex-direction: row;
    }

    .modal-footer .btn {
        width: auto;
        margin: 0 0.25rem;
    }
}

/* Medium Devices (small laptops, 992px and down) */
@media (max-width: 992px) {
    .breadcrumb {
        font-size: 0.8rem;
    }

    .topbar .h3 {
        font-size: 1.2rem;
    }

    .sidebar-brand-text {
        font-size: 0.85rem;
    }

    .collapse-item {
        padding: 0.5rem 1rem;
    }
}



/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Touch devices */
    .nav-link,
    .collapse-item,
    .btn,
    .dropdown-item {
        min-height: 44px; /* Apple's recommended minimum touch target */
        display: flex;
        align-items: center;
        touch-action: manipulation;
        -webkit-tap-highlight-color: rgba(0,0,0,0.1);
    }

    .nav-link:active,
    .collapse-item:active,
    .btn:active,
    .dropdown-item:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    /* Remove hover effects on touch devices */
    .nav-link:hover,
    .collapse-item:hover,
    .btn:hover,
    .dropdown-item:hover {
        transform: none;
        background-color: initial;
    }
}

/* High DPI Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .sidebar-brand-icon,
    .nav-link i,
    .btn i {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Landscape Orientation Optimizations */
@media (max-height: 500px) and (orientation: landscape) {
    .sidebar {
        overflow-y: auto;
        max-height: 100vh;
    }

    .sidebar .nav-item .nav-link {
        padding: 0.4rem 1rem;
        min-height: 2.2rem;
    }

    .sidebar-brand {
        padding: 0.5rem 1rem;
        min-height: 2.5rem;
    }

    .topbar {
        min-height: 3rem;
    }

    .topbar .h3 {
        font-size: 1rem;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .topbar,
    .sticky-footer,
    .scroll-to-top,
    .modal {
        display: none !important;
    }

    #content-wrapper {
        margin-left: 0 !important;
    }

    .container-fluid {
        padding: 0 !important;
    }

    .card {
        border: 1px solid #000 !important;
        page-break-inside: avoid;
    }

    .table {
        border-collapse: collapse !important;
    }

    .table th,
    .table td {
        border: 1px solid #000 !important;
    }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus Indicators for Keyboard Navigation */
.nav-link:focus,
.collapse-item:focus,
.btn:focus,
.dropdown-item:focus,
.form-control:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Skip to Content Link for Screen Readers */
.skip-to-content {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #007bff;
    color: white;
    padding: 8px;
    text-decoration: none;
    z-index: 9999;
    border-radius: 4px;
}

.skip-to-content:focus {
    top: 6px;
}

/* Smooth Transitions */
* {
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
                border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Override transitions for touch devices to improve performance */
@media (hover: none) and (pointer: coarse) {
    * {
        transition: none;
    }

    .nav-link,
    .collapse-item,
    .btn,
    .dropdown-item {
        transition: transform 0.1s ease;
    }
}

/* Focus States */
.nav-link:focus,
.collapse-item:focus,
.btn:focus {
    outline: 2px solid #4e73df;
    outline-offset: 2px;
}

/* Note: Sidebar text visibility is handled by sidebar-fix.css for better specificity */

/* Print Styles */
@media print {
    .sidebar,
    .topbar,
    .footer,
    .btn,
    .modal {
        display: none !important;
    }

    .content-wrapper {
        margin-left: 0 !important;
    }
}
