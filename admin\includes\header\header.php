<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title><?php echo isset($page_title) ? $page_title : 'Dashboard Admin'; ?> - Antosa Arsitek</title>

    <!-- Custom fonts for this template-->
    <link href="../tmp/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link
        href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="../tmp/css/sb-admin-2.min.css" rel="stylesheet">

    <!-- Custom CSS for enhanced styling -->
    <style>
        /* Profile image styling */
        .img-profile {
            width: 2rem;
            height: 2rem;
            object-fit: cover;
        }

        /* Topbar divider alignment */
        .topbar-divider {
            height: 2rem;
            margin: auto 1rem;
            align-self: center;
        }

        /* Topbar Navigation Alignment */
        .topbar .navbar-nav {
            align-items: center;
            height: 100%;
        }

        .topbar .nav-item {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .topbar .nav-link {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1rem;
            height: 3rem;
            transition: all 0.2s ease;
            position: relative;
        }

        .topbar .nav-link:hover {
            background-color: rgba(0,0,0,0.05);
            border-radius: 0.35rem;
        }

        /* Responsive Enhancements for Admin */
        @media (max-width: 576px) {
            /* Extra small devices (phones) */
            .topbar .nav-link {
                padding: 0.5rem 0.75rem;
                height: 2.5rem;
            }

            .img-profile {
                width: 1.8rem;
                height: 1.8rem;
            }

            .topbar-divider {
                height: 1.5rem;
                margin: auto 0.5rem;
            }

            .topbar .navbar-nav .nav-item .dropdown-menu {
                min-width: 12rem;
                max-width: 90vw;
                left: auto !important;
                right: 0 !important;
            }
        }

        @media (max-width: 768px) {
            /* Small devices (tablets) */
            .topbar .nav-link {
                padding: 0.6rem 0.8rem;
                height: 2.75rem;
            }

            .img-profile {
                width: 1.9rem;
                height: 1.9rem;
            }

            /* Hide user name on smaller screens */
            .topbar .nav-item .nav-link span {
                display: none !important;
            }
        }

        @media (min-width: 769px) and (max-width: 991px) {
            /* Medium devices (small laptops) */
            .topbar .nav-link {
                padding: 0.65rem 0.9rem;
                height: 2.85rem;
            }
        }

        /* Ensure dropdown is always accessible */
        .topbar .dropdown-menu {
            border: 1px solid rgba(0,0,0,.15);
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .topbar .dropdown-item {
            padding: 0.75rem 1.5rem;
            transition: all 0.2s ease;
        }

        .topbar .dropdown-item:hover {
            background-color: #f8f9fc;
            transform: translateX(5px);
        }
    </style>

</head>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">
