                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">

                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>

                    <!-- Page Title & Breadcrumb -->
                    <div class="d-flex align-items-center flex-grow-1">
                        <div class="mr-auto">
                            <h1 class="h3 mb-0 text-gray-800">
                                <?php echo isset($page_title) ? $page_title : 'Dashboard Proyek'; ?>
                            </h1>

                            <!-- Breadcrumb -->
                            <nav aria-label="breadcrumb" class="mt-1">
                                <ol class="breadcrumb mb-0 bg-transparent p-0 small">
                                <li class="breadcrumb-item">
                                    <a href="proyek.php"><i class="fas fa-home"></i> Dashboard</a>
                                </li>
                                <?php
                                $current_page = basename($_SERVER['PHP_SELF'], '.php');
                                $breadcrumb_map = [
                                    'input_tugas' => ['Manajemen Tugas', 'Input Tugas'],
                                    'tugas_harian' => ['Manajemen Tugas', 'Daftar Tugas'],
                                    'upload_file' => ['Manajemen File', 'Upload File'],
                                    'file_approved' => ['Manajemen File', 'File Disetujui'],
                                    'verifikasi' => ['Verifikasi & Approval']
                                ];

                                if (isset($breadcrumb_map[$current_page])) {
                                    foreach ($breadcrumb_map[$current_page] as $index => $crumb) {
                                        $is_last = ($index == count($breadcrumb_map[$current_page]) - 1);
                                        if ($is_last) {
                                            echo '<li class="breadcrumb-item active">' . $crumb . '</li>';
                                        } else {
                                            echo '<li class="breadcrumb-item">' . $crumb . '</li>';
                                        }
                                    }
                                }
                                ?>
                                </ol>
                            </nav>
                        </div>
                    </div>

                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ml-auto">

                        <!-- Nav Item - Alerts -->
                        <li class="nav-item dropdown no-arrow mx-1">
                            <a class="nav-link dropdown-toggle position-relative"
                               href="#" id="alertsDropdown" role="button"
                               data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-bell fa-fw"></i>
                                <!-- Counter - Alerts -->
                                <?php
                                require_once '../koneksi.php';
                                $pending_counts = getPendingCounts();
                                if ($pending_counts['total'] > 0) {
                                    echo '<span class="badge badge-danger badge-counter">' . $pending_counts['total'] . '</span>';
                                }
                                ?>
                            </a>
                            <!-- Dropdown - Alerts -->
                            <div class="dropdown-list dropdown-menu dropdown-menu-right shadow animated--grow-in"
                                aria-labelledby="alertsDropdown" style="min-width: 20rem;">
                                <h6 class="dropdown-header bg-primary text-white">
                                    <i class="fas fa-bell mr-2"></i>Notifikasi Verifikasi
                                </h6>
                                <?php
                                // Generate notification items using functions
                                echo generateNotificationItem($pending_counts['tugas'], 'tugas', 'fas fa-tasks', 'bg-warning');
                                echo generateNotificationItem($pending_counts['file'], 'file', 'fas fa-file', 'bg-info');
                                ?>

                                <?php if ($pending_counts['total'] == 0): ?>
                                <div class="dropdown-item text-center py-4">
                                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                    <div class="small text-gray-500">Tidak ada notifikasi baru</div>
                                </div>
                                <?php endif; ?>

                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item text-center py-3 bg-light" href="verifikasi.php">
                                    <i class="fas fa-eye mr-2"></i>
                                    <span class="font-weight-bold">Lihat Semua Verifikasi</span>
                                </a>
                            </div>
                        </li>

                        <div class="topbar-divider d-none d-sm-block"></div>

                        <!-- Nav Item - User Information -->
                        <li class="nav-item dropdown no-arrow">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="mr-2 d-none d-lg-inline text-gray-600 small">
                                    <?php echo isset($_SESSION['nama']) ? $_SESSION['nama'] : 'User'; ?>
                                </span>
                                <img class="img-profile rounded-circle"
                                    src="../tmp/img/undraw_profile.svg">
                            </a>
                            <!-- Dropdown - User Information -->
                            <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in"
                                aria-labelledby="userDropdown">
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Profil
                                </a>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-cogs fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Pengaturan
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#logoutModal">
                                    <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Keluar
                                </a>
                            </div>
                        </li>

                    </ul>

                </nav>
                <!-- End of Topbar -->

                <!-- Enhanced responsive styles for project topbar -->
                <style>
                    /* Enhanced Topbar Responsiveness for Project Management */
                    @media (max-width: 576px) {
                        /* Extra small devices (phones) */
                        .topbar {
                            padding: 0.5rem 0.75rem;
                            min-height: 3.5rem;
                        }

                        .topbar .h3 {
                            font-size: 1rem;
                            margin-bottom: 0.25rem;
                        }

                        .breadcrumb {
                            font-size: 0.7rem;
                            margin-bottom: 0;
                            padding: 0.25rem 0;
                        }

                        .breadcrumb-item {
                            max-width: 80px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }

                        .breadcrumb-item + .breadcrumb-item::before {
                            padding-right: 0.25rem;
                            padding-left: 0.25rem;
                            font-size: 0.6rem;
                        }

                        .topbar .navbar-nav .nav-item .nav-link {
                            padding: 0.4rem 0.6rem;
                            min-height: 2.3rem;
                        }

                        .topbar .img-profile {
                            width: 1.6rem;
                            height: 1.6rem;
                        }

                        .topbar .dropdown-menu {
                            min-width: 12rem;
                            max-width: 90vw;
                            left: auto !important;
                            right: 0 !important;
                            margin-top: 0.5rem;
                        }

                        .topbar .dropdown-item {
                            padding: 0.6rem 1rem;
                            font-size: 0.85rem;
                        }

                        .topbar .dropdown-list {
                            min-width: 16rem;
                            max-width: 90vw;
                        }

                        .topbar .dropdown-list .dropdown-item {
                            padding: 0.75rem 1rem;
                        }

                        .badge-counter {
                            min-width: 1rem;
                            height: 1rem;
                            font-size: 0.6rem;
                            top: 0.2rem;
                            right: 0.2rem;
                        }

                        /* Hide user name on very small screens */
                        .topbar .nav-item .nav-link span {
                            display: none !important;
                        }

                        /* Sidebar toggle button optimization */
                        #sidebarToggleTop {
                            min-width: 2.3rem;
                            min-height: 2.3rem;
                            padding: 0.4rem;
                            margin-right: 0.5rem;
                        }

                        /* Notification dropdown optimizations */
                        .dropdown-list .dropdown-item {
                            padding: 0.5rem 0.75rem;
                        }

                        .icon-circle {
                            width: 2rem;
                            height: 2rem;
                            font-size: 0.8rem;
                        }
                    }

                    @media (max-width: 768px) {
                        /* Small devices (tablets) */
                        .topbar {
                            padding: 0.6rem 1rem;
                            min-height: 3.75rem;
                        }

                        .topbar .h3 {
                            font-size: 1.1rem;
                        }

                        .breadcrumb {
                            font-size: 0.75rem;
                        }

                        .breadcrumb-item {
                            max-width: 100px;
                        }

                        .topbar .navbar-nav .nav-item .nav-link {
                            padding: 0.5rem 0.7rem;
                            min-height: 2.5rem;
                        }

                        .topbar .img-profile {
                            width: 1.8rem;
                            height: 1.8rem;
                        }

                        .topbar .dropdown-menu {
                            min-width: 14rem;
                        }

                        .badge-counter {
                            min-width: 1.1rem;
                            height: 1.1rem;
                            font-size: 0.65rem;
                        }

                        #sidebarToggleTop {
                            min-width: 2.5rem;
                            min-height: 2.5rem;
                            padding: 0.5rem;
                        }
                    }

                    @media (min-width: 769px) and (max-width: 991px) {
                        /* Medium devices (small laptops) */
                        .topbar .h3 {
                            font-size: 1.2rem;
                        }

                        .breadcrumb {
                            font-size: 0.8rem;
                        }

                        .topbar .navbar-nav .nav-item .nav-link {
                            padding: 0.6rem 0.8rem;
                            min-height: 2.75rem;
                        }
                    }

                    /* Touch-friendly enhancements */
                    .topbar .nav-item .nav-link {
                        touch-action: manipulation;
                        -webkit-tap-highlight-color: rgba(0,0,0,0.05);
                        transition: all 0.2s ease;
                    }

                    .topbar .nav-item .nav-link:active {
                        background-color: rgba(0,0,0,0.1);
                        transform: scale(0.98);
                        transition: all 0.1s ease;
                    }

                    .topbar .dropdown-item {
                        touch-action: manipulation;
                        -webkit-tap-highlight-color: rgba(58, 59, 69, 0.1);
                        transition: all 0.2s ease;
                    }

                    .topbar .dropdown-item:active {
                        background-color: #f8f9fc;
                        transform: scale(0.98);
                    }

                    /* Improve sidebar toggle button */
                    #sidebarToggleTop {
                        touch-action: manipulation;
                        -webkit-tap-highlight-color: transparent;
                        transition: all 0.2s ease;
                    }

                    #sidebarToggleTop:active {
                        transform: scale(0.95);
                        background-color: rgba(0,0,0,0.1);
                    }

                    /* Breadcrumb responsive improvements */
                    .breadcrumb {
                        flex-wrap: wrap;
                        word-break: break-word;
                    }

                    .breadcrumb-item a {
                        color: #6c757d;
                        text-decoration: none;
                        transition: color 0.2s ease;
                    }

                    .breadcrumb-item a:hover {
                        color: #495057;
                        text-decoration: underline;
                    }
                </style>
