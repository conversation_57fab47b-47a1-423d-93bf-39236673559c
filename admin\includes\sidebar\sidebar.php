        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="index.html">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div class="sidebar-brand-text mx-3">antosa</div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item">
                <a class="nav-link" href="admin.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Dashboard</span></a>         
            </li>
          
            
            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">
                Interface
            </div>

            <!-- Nav Item - Kelola User Proyek -->
            <li class="nav-item">
                <a class="nav-link" href="admin.php?url=kelola_user_proyek">
                    <i class="fas fa-fw fa-users"></i>
                    <span>Kelola User Proyek</span></a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

             <li class="nav-item">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span></a>
            </li>
            
            <!-- Sidebar Toggler (Sidebar) - Enhanced for better touch support -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"
                        style="min-width: 2.5rem; min-height: 2.5rem; touch-action: manipulation;"></button>
            </div>

        </ul>
        <!-- End of Sidebar -->

        <!-- Additional responsive styles for sidebar -->
        <style>
            /* Enhanced Sidebar Responsiveness */
            @media (max-width: 576px) {
                /* Extra small devices (phones) */
                .sidebar {
                    width: 100% !important;
                }

                .sidebar.toggled {
                    width: 0 !important;
                }

                .sidebar .nav-item .nav-link {
                    padding: 0.75rem 1rem;
                    min-height: 3rem;
                    display: flex;
                    align-items: center;
                }

                .sidebar .nav-item .nav-link i {
                    margin-right: 0.75rem;
                    min-width: 1.5rem;
                    text-align: center;
                }

                .sidebar-brand {
                    padding: 1rem 0.5rem;
                    min-height: 4rem;
                }

                .sidebar-brand-text {
                    font-size: 0.9rem !important;
                }

                .sidebar-divider {
                    margin: 0.5rem 0;
                }

                .sidebar-heading {
                    font-size: 0.7rem;
                    padding: 0.5rem 1rem 0.25rem;
                }
            }

            @media (max-width: 768px) {
                /* Small devices (tablets) */
                .sidebar .nav-item .nav-link {
                    padding: 0.7rem 1rem;
                    min-height: 2.8rem;
                }

                .sidebar-brand {
                    padding: 0.75rem 0.5rem;
                    min-height: 3.5rem;
                }

                .sidebar-brand-text {
                    font-size: 0.85rem !important;
                }
            }

            /* Touch-friendly enhancements */
            .sidebar .nav-item .nav-link {
                touch-action: manipulation;
                -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
            }

            .sidebar .nav-item .nav-link:active {
                background-color: rgba(255, 255, 255, 0.1);
                transform: scale(0.98);
                transition: all 0.1s ease;
            }

            /* Improve sidebar toggle button */
            #sidebarToggle {
                touch-action: manipulation;
                -webkit-tap-highlight-color: transparent;
                transition: all 0.2s ease;
            }

            #sidebarToggle:active {
                transform: scale(0.95);
            }
        </style>
