<!DOCTYPE html>
<html lang="id">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Sistem Manajemen Proyek Arsitek - Antosa Arsitek">
    <meta name="author" content="Antosa Arsitek">
    <meta name="keywords" content="arsitek, proyek, manajemen, desain, konstruksi">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../tmp/img/favicon.ico">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo isset($page_title) ? $page_title : 'Dashboard Proyek'; ?> - Antosa Arsitek">
    <meta property="og:description" content="Sistem Manajemen Proyek Arsitek">
    <meta property="og:type" content="website">

    <title><?php echo isset($page_title) ? $page_title : 'Dashboard Proyek'; ?> - Antosa Arsitek</title>

    <!-- Custom fonts for this template-->
    <link href="../tmp/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link
        href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="../tmp/css/sb-admin-2.min.css" rel="stylesheet">

    <!-- Custom CSS for enhanced styling -->
    <link href="includes/custom-styles.css" rel="stylesheet">

    <!-- Include functions for reusable components -->
    <?php require_once 'includes/fungsi.php'; ?>

    <!-- Critical inline CSS for sidebar visibility - kept inline for highest priority -->
    <style>
        /* Critical sidebar text visibility fix - inline for highest priority */
        .sidebar-dark .navbar-nav .nav-item .nav-link,
        .sidebar-dark .navbar-nav .nav-item .nav-link span,
        .sidebar-dark .navbar-nav .nav-item .nav-link i,
        .sidebar .nav-item .nav-link,
        .sidebar .nav-item .nav-link span,
        .sidebar .nav-item .nav-link i {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .sidebar-dark .navbar-nav .nav-item .nav-link:hover,
        .sidebar-dark .navbar-nav .nav-item .nav-link:hover span,
        .sidebar-dark .navbar-nav .nav-item .nav-link:hover i,
        .sidebar .nav-item .nav-link:hover,
        .sidebar .nav-item .nav-link:hover span,
        .sidebar .nav-item .nav-link:hover i {
            color: #fff !important;
        }

        .sidebar-dark .navbar-nav .nav-item.active .nav-link,
        .sidebar-dark .navbar-nav .nav-item.active .nav-link span,
        .sidebar-dark .navbar-nav .nav-item.active .nav-link i,
        .sidebar .nav-item.active .nav-link,
        .sidebar .nav-item.active .nav-link span,
        .sidebar .nav-item.active .nav-link i {
            color: #fff !important;
        }

        /* Force visibility for all sidebar text elements */
        .sidebar * {
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* Responsive Header Enhancements */
        @media (max-width: 576px) {
            /* Extra small devices (phones) */
            .container-fluid {
                padding-left: 0.75rem;
                padding-right: 0.75rem;
            }

            /* Ensure proper spacing on mobile */
            .topbar {
                padding: 0.5rem 0.75rem;
            }

            .topbar .h3 {
                font-size: 1.1rem;
            }

            .breadcrumb {
                font-size: 0.7rem;
                margin-bottom: 0;
            }

            .breadcrumb-item + .breadcrumb-item::before {
                padding-right: 0.25rem;
                padding-left: 0.25rem;
            }
        }

        @media (max-width: 768px) {
            /* Small devices (tablets) */
            .topbar .h3 {
                font-size: 1.2rem;
            }

            .breadcrumb {
                font-size: 0.75rem;
            }

            /* Optimize sidebar brand for mobile */
            .sidebar-brand-text {
                font-size: 0.8rem !important;
            }

            .sidebar-brand-icon {
                font-size: 1.2rem !important;
            }
        }

        @media (min-width: 769px) and (max-width: 991px) {
            /* Medium devices (small laptops) */
            .topbar .h3 {
                font-size: 1.3rem;
            }

            .sidebar-brand-text {
                font-size: 0.85rem !important;
            }
        }

        /* Ensure content wrapper is responsive */
        #content-wrapper {
            min-height: 100vh;
            overflow-x: hidden;
        }

        #content {
            flex: 1 0 auto;
        }
    </style>

</head>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">
