                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">

                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>

                    <!-- Page Title -->
                    <div class="d-flex align-items-center flex-grow-1">
                        <h1 class="h3 mb-0 text-gray-800 mr-auto">
                            <?php echo isset($page_title) ? $page_title : 'Dashboard Admin'; ?>
                        </h1>
                    </div>

                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ml-auto">

                        <div class="topbar-divider d-none d-sm-block"></div>

                        <!-- Nav Item - User Information -->
                        <li class="nav-item dropdown no-arrow">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="mr-2 d-none d-lg-inline text-gray-600 small">
                                    <?php echo isset($_SESSION['nama']) ? $_SESSION['nama'] : 'User'; ?>
                                </span>
                                <img class="img-profile rounded-circle"
                                    src="../tmp/img/undraw_profile.svg">
                            </a>
                            <!-- Dropdown - User Information -->
                            <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in"
                                aria-labelledby="userDropdown">
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Profil
                                </a>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-cogs fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Pengaturan
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#logoutModal">
                                    <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Keluar
                                </a>
                            </div>
                        </li>

                    </ul>

                </nav>
                <!-- End of Topbar -->

                <!-- Enhanced responsive styles for admin topbar -->
                <style>
                    /* Enhanced Topbar Responsiveness for Admin */
                    @media (max-width: 576px) {
                        /* Extra small devices (phones) */
                        .topbar {
                            padding: 0.5rem 0.75rem;
                            min-height: 3.5rem;
                        }

                        .topbar .h3 {
                            font-size: 1.1rem;
                            margin-bottom: 0;
                        }

                        .topbar .navbar-nav .nav-item .nav-link {
                            padding: 0.5rem 0.75rem;
                            min-height: 2.5rem;
                        }

                        .topbar .img-profile {
                            width: 1.8rem;
                            height: 1.8rem;
                        }

                        .topbar .dropdown-menu {
                            min-width: 12rem;
                            max-width: 90vw;
                            left: auto !important;
                            right: 0 !important;
                            margin-top: 0.5rem;
                        }

                        .topbar .dropdown-item {
                            padding: 0.75rem 1rem;
                            font-size: 0.9rem;
                        }

                        .topbar-divider {
                            height: 1.5rem;
                            margin: auto 0.5rem;
                        }

                        /* Hide user name on very small screens */
                        .topbar .nav-item .nav-link span {
                            display: none !important;
                        }

                        /* Sidebar toggle button optimization */
                        #sidebarToggleTop {
                            min-width: 2.5rem;
                            min-height: 2.5rem;
                            padding: 0.5rem;
                        }
                    }

                    @media (max-width: 768px) {
                        /* Small devices (tablets) */
                        .topbar {
                            padding: 0.6rem 1rem;
                            min-height: 3.75rem;
                        }

                        .topbar .h3 {
                            font-size: 1.2rem;
                        }

                        .topbar .navbar-nav .nav-item .nav-link {
                            padding: 0.6rem 0.8rem;
                            min-height: 2.75rem;
                        }

                        .topbar .img-profile {
                            width: 1.9rem;
                            height: 1.9rem;
                        }

                        .topbar .dropdown-menu {
                            min-width: 14rem;
                        }

                        .topbar-divider {
                            height: 1.75rem;
                            margin: auto 0.75rem;
                        }
                    }

                    @media (min-width: 769px) and (max-width: 991px) {
                        /* Medium devices (small laptops) */
                        .topbar .h3 {
                            font-size: 1.3rem;
                        }

                        .topbar .navbar-nav .nav-item .nav-link {
                            padding: 0.65rem 0.9rem;
                            min-height: 2.85rem;
                        }
                    }

                    /* Touch-friendly enhancements */
                    .topbar .nav-item .nav-link {
                        touch-action: manipulation;
                        -webkit-tap-highlight-color: rgba(0,0,0,0.05);
                        transition: all 0.2s ease;
                    }

                    .topbar .nav-item .nav-link:active {
                        background-color: rgba(0,0,0,0.1);
                        transform: scale(0.98);
                        transition: all 0.1s ease;
                    }

                    .topbar .dropdown-item {
                        touch-action: manipulation;
                        -webkit-tap-highlight-color: rgba(58, 59, 69, 0.1);
                        transition: all 0.2s ease;
                    }

                    .topbar .dropdown-item:active {
                        background-color: #f8f9fc;
                        transform: scale(0.98);
                    }

                    /* Improve sidebar toggle button */
                    #sidebarToggleTop {
                        touch-action: manipulation;
                        -webkit-tap-highlight-color: transparent;
                        transition: all 0.2s ease;
                    }

                    #sidebarToggleTop:active {
                        transform: scale(0.95);
                        background-color: rgba(0,0,0,0.1);
                    }
                </style>
