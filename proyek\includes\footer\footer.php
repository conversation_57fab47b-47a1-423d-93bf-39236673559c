            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="row">
                        <div class="col-md-6 col-12">
                            <div class="copyright text-left text-center text-md-left my-auto">
                                <span>&copy; <?php echo date('Y'); ?> <strong>Antosa Arsitek</strong>. Sistem Manajemen Proyek</span>
                            </div>
                        </div>
                        <div class="col-md-6 col-12">
                            <div class="text-right text-center text-md-right my-auto">
                                <small class="text-muted">
                                    <span class="d-block d-md-inline">Versi 2.0</span>
                                    <span class="d-none d-md-inline"> | </span>
                                    <span class="d-block d-md-inline">
                                        <i class="fas fa-user"></i> <?php echo isset($_SESSION['nama']) ? $_SESSION['nama'] : 'User'; ?>
                                    </span>
                                    <span class="d-none d-md-inline"> | </span>
                                    <span class="d-block d-md-inline">
                                        <i class="fas fa-clock"></i> <?php echo date('d M Y, H:i'); ?>
                                    </span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="logoutModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="logoutModalLabel">
                        <i class="fas fa-sign-out-alt"></i> Konfirmasi Keluar
                    </h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="fas fa-question-circle fa-3x text-warning mb-3"></i>
                        <p>Apakah Anda yakin ingin keluar dari sistem?</p>
                        <small class="text-muted">Sesi Anda akan berakhir dan Anda perlu login kembali.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <a class="btn btn-danger" href="../logout.php">
                        <i class="fas fa-sign-out-alt"></i> Ya, Keluar
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced responsive styles for project footer -->
    <style>
        /* Enhanced Footer Responsiveness for Project Management */
        @media (max-width: 576px) {
            /* Extra small devices (phones) */
            .sticky-footer {
                padding: 1rem 0;
            }

            .sticky-footer .container {
                padding-left: 0.75rem;
                padding-right: 0.75rem;
            }

            .sticky-footer .copyright {
                font-size: 0.8rem;
                margin-bottom: 0.5rem;
                text-align: center !important;
            }

            .sticky-footer .text-muted {
                font-size: 0.7rem;
                text-align: center !important;
            }

            .sticky-footer .row > div {
                margin-bottom: 0.5rem;
            }

            .sticky-footer .row > div:last-child {
                margin-bottom: 0;
            }

            /* Scroll to top button optimization */
            .scroll-to-top {
                width: 2.5rem;
                height: 2.5rem;
                bottom: 1rem;
                right: 1rem;
                font-size: 1rem;
            }

            /* Modal optimizations for mobile */
            .modal-dialog {
                margin: 1rem 0.5rem;
                max-width: calc(100% - 1rem);
            }

            .modal-content {
                border-radius: 0.5rem;
            }

            .modal-header {
                padding: 1rem 1rem 0.5rem;
            }

            .modal-body {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }

            .modal-footer {
                padding: 0.5rem 1rem 1rem;
                flex-direction: column;
            }

            .modal-footer .btn {
                width: 100%;
                margin: 0.25rem 0;
            }

            .modal-footer .btn + .btn {
                margin-left: 0;
            }
        }

        @media (max-width: 768px) {
            /* Small devices (tablets) */
            .sticky-footer {
                padding: 1.25rem 0;
            }

            .sticky-footer .copyright {
                font-size: 0.85rem;
                margin-bottom: 0.75rem;
                text-align: center !important;
            }

            .sticky-footer .text-muted {
                font-size: 0.75rem;
                text-align: center !important;
            }

            .scroll-to-top {
                width: 2.75rem;
                height: 2.75rem;
                bottom: 1.25rem;
                right: 1.25rem;
                font-size: 1.1rem;
            }

            .modal-dialog {
                margin: 1.5rem 1rem;
                max-width: calc(100% - 2rem);
            }

            .modal-footer {
                flex-direction: row;
            }

            .modal-footer .btn {
                width: auto;
                margin: 0 0.25rem;
            }
        }

        @media (min-width: 769px) {
            /* Medium and larger devices */
            .sticky-footer .copyright {
                text-align: left !important;
            }

            .sticky-footer .text-right {
                text-align: right !important;
            }
        }

        /* Touch-friendly enhancements */
        .scroll-to-top {
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
            transition: all 0.2s ease;
        }

        .scroll-to-top:active {
            transform: scale(0.95);
        }

        .modal-footer .btn {
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
            transition: all 0.2s ease;
            min-height: 2.5rem;
            padding: 0.5rem 1rem;
        }

        .modal-footer .btn:active {
            transform: scale(0.98);
        }

        .modal .close {
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
            padding: 0.5rem;
            margin: -0.5rem -0.5rem -0.5rem auto;
            min-width: 2.5rem;
            min-height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal .close:active {
            transform: scale(0.95);
        }

        /* Footer text responsive improvements */
        .sticky-footer .text-muted i {
            margin-right: 0.25rem;
        }

        .sticky-footer .text-muted span {
            white-space: nowrap;
        }
    </style>

    <!-- Bootstrap core JavaScript -->
    <script src="../tmp/vendor/jquery/jquery.min.js"></script>
    <script src="../tmp/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript -->
    <script src="../tmp/vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages -->
    <script src="../tmp/js/sb-admin-2.min.js"></script>

    <!-- Optional: Chart plugin -->
    <script src="../tmp/vendor/chart.js/Chart.min.js"></script>
    <script src="../tmp/js/demo/chart-area-demo.js"></script>
    <script src="../tmp/js/demo/chart-pie-demo.js"></script>

</body>
</html>
