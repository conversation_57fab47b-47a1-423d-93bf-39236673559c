        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="proyek.php">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-drafting-compass"></i>
                </div>
                <div class="sidebar-brand-text mx-3">Antosa Arsitek</div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'proyek.php') ? 'active' : ''; ?>">
                <a class="nav-link" href="proyek.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">
                Manajemen Proyek
            </div>

            <!-- Nav Item - Manajemen Tugas (Collapsible) -->
            <li class="nav-item">
                <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseTugas"
                    aria-expanded="true" aria-controls="collapseTugas">
                    <i class="fas fa-fw fa-folder-open"></i>
                    <span>Manajemen Tugas</span>
                </a>
                <div id="collapseTugas" class="collapse <?php echo (in_array(basename($_SERVER['PHP_SELF']), ['input_tugas.php', 'tugas_harian.php'])) ? 'show' : ''; ?>"
                     aria-labelledby="headingTugas" data-parent="#accordionSidebar">
                    <div class="bg-white py-2 collapse-inner rounded">
                        <h6 class="collapse-header">Kelola Tugas:</h6>
                        <a class="collapse-item <?php echo (basename($_SERVER['PHP_SELF']) == 'input_tugas.php') ? 'active' : ''; ?>"
                           href="input_tugas.php">
                            <i class="fas fa-plus-circle"></i> Input Tugas Harian
                        </a>
                        <a class="collapse-item <?php echo (basename($_SERVER['PHP_SELF']) == 'tugas_harian.php') ? 'active' : ''; ?>"
                           href="tugas_harian.php">
                            <i class="fas fa-tasks"></i> Daftar Tugas Harian
                        </a>
                    </div>
                </div>
            </li>

            <!-- Nav Item - Manajemen File (Collapsible) -->
            <li class="nav-item">
                <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseFile"
                    aria-expanded="true" aria-controls="collapseFile">
                    <i class="fas fa-fw fa-folder"></i>
                    <span>Manajemen File</span>
                </a>
                <div id="collapseFile" class="collapse <?php echo (in_array(basename($_SERVER['PHP_SELF']), ['upload_file.php', 'file_approved.php'])) ? 'show' : ''; ?>"
                     aria-labelledby="headingFile" data-parent="#accordionSidebar">
                    <div class="bg-white py-2 collapse-inner rounded">
                        <h6 class="collapse-header">Kelola File:</h6>
                        <a class="collapse-item <?php echo (basename($_SERVER['PHP_SELF']) == 'upload_file.php') ? 'active' : ''; ?>"
                           href="upload_file.php">
                            <i class="fas fa-upload"></i> Upload File Desain
                        </a>
                        <a class="collapse-item <?php echo (basename($_SERVER['PHP_SELF']) == 'file_approved.php') ? 'active' : ''; ?>"
                           href="file_approved.php">
                            <i class="fas fa-check-circle"></i> File Disetujui
                        </a>
                    </div>
                </div>
            </li>
            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Nav Item - Verifikasi & Approval -->
            <li class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'verifikasi.php') ? 'active' : ''; ?>">
                <a class="nav-link" href="verifikasi.php">
                    <i class="fas fa-fw fa-clipboard-check"></i>
                    <span>Verifikasi & Approval</span>
                </a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

            <!-- Heading -->
            <div class="sidebar-heading">
                Sistem
            </div>

            <!-- Nav Item - Logout -->
            <li class="nav-item">
                <a class="nav-link" href="#" data-toggle="modal" data-target="#logoutModal">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span>
                </a>
            </li>

            <!-- Sidebar Toggler (Sidebar) - Enhanced for better touch support -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"
                        style="min-width: 2.5rem; min-height: 2.5rem; touch-action: manipulation;"></button>
            </div>

        </ul>
        <!-- End of Sidebar -->

        <!-- Enhanced responsive styles for project sidebar -->
        <style>
            /* Enhanced Sidebar Responsiveness for Project Management */
            @media (max-width: 576px) {
                /* Extra small devices (phones) */
                .sidebar {
                    width: 100% !important;
                }

                .sidebar.toggled {
                    width: 0 !important;
                }

                .sidebar .nav-item .nav-link {
                    padding: 0.75rem 1rem;
                    min-height: 3rem;
                    display: flex;
                    align-items: center;
                }

                .sidebar .nav-item .nav-link i {
                    margin-right: 0.75rem;
                    min-width: 1.5rem;
                    text-align: center;
                }

                .sidebar-brand {
                    padding: 1rem 0.5rem;
                    min-height: 4rem;
                }

                .sidebar-brand-text {
                    font-size: 0.8rem !important;
                }

                .sidebar-divider {
                    margin: 0.5rem 0;
                }

                .sidebar-heading {
                    font-size: 0.7rem;
                    padding: 0.5rem 1rem 0.25rem;
                }

                /* Collapsible menu optimizations */
                .collapse-inner {
                    padding: 0.5rem 0;
                }

                .collapse-item {
                    padding: 0.5rem 1.5rem;
                    margin: 0.1rem 0;
                    min-height: 2.5rem;
                    display: flex;
                    align-items: center;
                }

                .collapse-header {
                    font-size: 0.65rem;
                    padding: 0.5rem 1.5rem 0.25rem;
                }
            }

            @media (max-width: 768px) {
                /* Small devices (tablets) */
                .sidebar .nav-item .nav-link {
                    padding: 0.7rem 1rem;
                    min-height: 2.8rem;
                }

                .sidebar-brand {
                    padding: 0.75rem 0.5rem;
                    min-height: 3.5rem;
                }

                .sidebar-brand-text {
                    font-size: 0.85rem !important;
                }

                .collapse-item {
                    padding: 0.45rem 1.25rem;
                    min-height: 2.3rem;
                }
            }

            /* Touch-friendly enhancements */
            .sidebar .nav-item .nav-link {
                touch-action: manipulation;
                -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
                transition: all 0.2s ease;
            }

            .sidebar .nav-item .nav-link:active {
                background-color: rgba(255, 255, 255, 0.1);
                transform: scale(0.98);
                transition: all 0.1s ease;
            }

            .collapse-item {
                touch-action: manipulation;
                -webkit-tap-highlight-color: rgba(58, 59, 69, 0.1);
                transition: all 0.2s ease;
            }

            .collapse-item:active {
                background-color: #f8f9fc;
                transform: scale(0.98);
            }

            /* Improve sidebar toggle button */
            #sidebarToggle {
                touch-action: manipulation;
                -webkit-tap-highlight-color: transparent;
                transition: all 0.2s ease;
            }

            #sidebarToggle:active {
                transform: scale(0.95);
            }

            /* Ensure collapsible menus work well on mobile */
            .collapse.show {
                display: block !important;
            }

            .collapse {
                transition: height 0.35s ease;
            }
        </style>
